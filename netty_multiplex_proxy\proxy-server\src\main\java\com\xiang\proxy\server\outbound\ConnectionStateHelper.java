package com.xiang.proxy.server.outbound;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 连接状态辅助工具类
 * 提供连接状态检查、超时处理和状态转换的辅助方法
 */
public class ConnectionStateHelper {
    private static final Logger logger = LoggerFactory.getLogger(ConnectionStateHelper.class);
    
    // 默认超时配置
    public static final long DEFAULT_CONNECTION_TIMEOUT = 30000; // 30秒

    /**
     *  检查连接是否正在创建中
     * @param connection
     * @param timeoutMs
     * @return
     */
    public static boolean isCreating(OutboundConnection connection, long timeoutMs) {
        if (connection == null) {
            return false;
        }

        // 创建中状态需要检查超时
        if (connection.isCreating()) {
            long stateAge = System.currentTimeMillis() - connection.getStateChangeTime();
            if (stateAge > timeoutMs) {
                logger.warn("连接创建超时: connectionId={}, age={}ms, timeout={}ms",
                        connection.getConnectionId(), stateAge, timeoutMs);
                // 标记为失败
                connection.markFailed();
                return false;
            }
            // 创建中但未超时，可以等待或缓存数据
            return true;
        }

        return false;
    }
    /**
     * 检查连接是否可用于发送数据
     * 
     * @param connection 连接对象
     * @param timeoutMs 超时时间（毫秒）
     * @return 是否可用
     */
    public static boolean isConnectionUsableForSending(OutboundConnection connection, long timeoutMs) {
        if (connection == null) {
            return false;
        }
        
        ConnectionState state = connection.getConnectionState();
        
        // 已连接状态直接可用
        if (connection.isConnected() && connection.isReusable()) {
            return true;
        }
        
        // 创建中状态需要检查超时
        if (isCreating(connection, timeoutMs)) {
            // 创建中但未超时，可以等待或缓存数据
            return true;
        }
        
        // 其他状态不可用
        logger.debug("连接状态不可用于发送数据: connectionId={}, state={}", 
                    connection.getConnectionId(), state);
        return false;
    }
    
    /**
     * 检查连接是否可复用
     * 
     * @param connection 连接对象
     * @param maxAge 最大连接年龄（毫秒）
     * @return 是否可复用
     */
    public static boolean isConnectionReusable(OutboundConnection connection, long maxAge) {
        if (connection == null) {
            return false;
        }
        
        // 检查连接状态
        if (!connection.isReusable()) {
            return false;
        }
        
        // 检查连接年龄
        long connectionAge = connection.getConnectionAge();
        if (connectionAge > maxAge) {
            logger.debug("连接过旧，不可复用: connectionId={}, age={}ms, maxAge={}ms",
                        connection.getConnectionId(), connectionAge, maxAge);
            return false;
        }
        
        return true;
    }
    
    /**
     * 安全地关闭连接
     * 根据连接当前状态选择合适的关闭方式
     * 
     * @param connection 连接对象
     * @param reason 关闭原因
     * @return 是否成功启动关闭过程
     */
    public static boolean safeCloseConnection(OutboundConnection connection, String reason) {
        if (connection == null) {
            return false;
        }
        
        ConnectionState currentState = connection.getConnectionState();
        logger.debug("安全关闭连接: connectionId={}, currentState={}, reason={}", 
                    connection.getConnectionId(), currentState, reason);
        
        switch (currentState) {
            case CREATING:
                // 连接正在创建中，标记为失败
                return connection.markFailed();
                
            case CONNECTED:
                // 连接已建立，正常关闭
                return connection.markClosing();
                
            case CLOSING:
                // 已经在关闭中，不需要重复操作
                logger.debug("连接已在关闭中: connectionId={}", connection.getConnectionId());
                return true;
                
            case CLOSED:
            case FAILED:
            case ERROR:
                // 连接已经处于终态，不需要操作
                logger.debug("连接已处于终态: connectionId={}, state={}", 
                            connection.getConnectionId(), currentState);
                return true;
                
            default:
                logger.warn("未知连接状态: connectionId={}, state={}", 
                           connection.getConnectionId(), currentState);
                return false;
        }
    }
    
    /**
     * 检查并处理连接超时
     * 
     * @param connection 连接对象
     * @param timeoutMs 超时时间（毫秒）
     * @return 是否处理了超时
     */
    public static boolean checkAndHandleTimeout(OutboundConnection connection, long timeoutMs) {
        if (connection == null) {
            return false;
        }
        
        // 只检查创建中的连接
        if (!connection.isCreating()) {
            return false;
        }
        
        long stateAge = System.currentTimeMillis() - connection.getStateChangeTime();
        if (stateAge > timeoutMs) {
            logger.warn("检测到连接创建超时，标记为失败: connectionId={}, age={}ms, timeout={}ms", 
                       connection.getConnectionId(), stateAge, timeoutMs);
            return connection.markFailed();
        }
        
        return false;
    }
    
    /**
     * 获取连接状态描述信息
     * 
     * @param connection 连接对象
     * @return 状态描述
     */
    public static String getConnectionStateInfo(OutboundConnection connection) {
        if (connection == null) {
            return "null";
        }
        
        ConnectionState state = connection.getConnectionState();
        long stateAge = System.currentTimeMillis() - connection.getStateChangeTime();
        long connectionAge = connection.getConnectionAge();
        
        return String.format("connectionId=%s, state=%s, stateAge=%dms, connectionAge=%dms, reusable=%s",
                           connection.getConnectionId(), state, stateAge, connectionAge, connection.isReusable());
    }
    
    /**
     * 验证状态转换是否合理
     * 
     * @param connection 连接对象
     * @param targetState 目标状态
     * @param context 上下文信息
     * @return 是否应该执行转换
     */
    public static boolean validateStateTransition(OutboundConnection connection, ConnectionState targetState, String context) {
        if (connection == null || targetState == null) {
            return false;
        }
        
        ConnectionState currentState = connection.getConnectionState();
        boolean canTransition = currentState.canTransitionTo(targetState);
        
        if (!canTransition) {
            logger.warn("状态转换验证失败: connectionId={}, {} -> {}, context={}", 
                       connection.getConnectionId(), currentState, targetState, context);
        } else {
            logger.debug("状态转换验证通过: connectionId={}, {} -> {}, context={}", 
                        connection.getConnectionId(), currentState, targetState, context);
        }
        
        return canTransition;
    }
    
    /**
     * 批量检查连接状态并清理无效连接
     * 
     * @param connections 连接映射
     * @param timeoutMs 超时时间
     * @return 清理的连接数量
     */
    public static int cleanupInvalidConnections(java.util.Map<String, OutboundConnection> connections, long timeoutMs) {
        if (connections == null || connections.isEmpty()) {
            return 0;
        }
        
        int cleanupCount = 0;
        long currentTime = System.currentTimeMillis();
        
        // 使用迭代器安全地移除元素
        var iterator = connections.entrySet().iterator();
        while (iterator.hasNext()) {
            var entry = iterator.next();
            OutboundConnection connection = entry.getValue();
            
            // 检查连接是否需要清理
            if (shouldCleanupConnection(connection, currentTime, timeoutMs)) {
                logger.debug("清理无效连接: key={}, {}", 
                            entry.getKey(), getConnectionStateInfo(connection));
                iterator.remove();
                cleanupCount++;
            }
        }
        
        if (cleanupCount > 0) {
            logger.info("批量清理无效连接完成: 清理数量={}, 剩余连接数={}", cleanupCount, connections.size());
        }
        
        return cleanupCount;
    }
    
    /**
     * 判断连接是否应该被清理
     */
    private static boolean shouldCleanupConnection(OutboundConnection connection, long currentTime, long timeoutMs) {
        if (connection == null) {
            return true;
        }
        
        ConnectionState state = connection.getConnectionState();
        
        // 终态连接在一定时间后清理
        if (state.isTerminal()) {
            long stateAge = currentTime - connection.getStateChangeTime();
            return stateAge > 300000; // 5分钟后清理终态连接
        }
        
        // 创建中的连接检查超时
        if (connection.isCreating()) {
            long stateAge = currentTime - connection.getStateChangeTime();
            if (stateAge > timeoutMs) {
                // 标记为失败
                connection.markFailed();
                return true;
            }
        }
        
        // 已连接的连接检查是否仍然活跃
        if (connection.isConnected()) {
            if (!connection.isActive()) {
                // 标记为错误
                connection.markError();
                return true;
            }
        }
        
        return false;
    }
}
