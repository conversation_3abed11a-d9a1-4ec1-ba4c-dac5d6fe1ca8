package com.xiang.proxy.server.inbound.impl.multiplex;

import com.xiang.proxy.server.core.ProxyRequest;
import com.xiang.proxy.server.util.ConnectionKeyUtils;
import io.netty.channel.Channel;
import io.netty.channel.embedded.EmbeddedChannel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;



import static org.junit.jupiter.api.Assertions.*;

/**
 * MultiplexSession 一致性测试
 * 验证修复后的 clientId 一致性、队列路由和连接复用功能
 *
 * 注意：此测试不依赖 Mockito，专注于测试核心的键值生成和一致性逻辑
 */
public class MultiplexSessionConsistencyTest {

    private Channel testChannel;
    private long clientConnectionId = 12345L;

    @BeforeEach
    void setUp() {
        // 创建测试通道
        testChannel = new EmbeddedChannel();
    }

    /**
     * 测试 clientId 生成的一致性
     */
    @Test
    void testClientIdConsistency() {
        // 测试通道ID生成的一致性
        String clientId1 = testChannel.id().asShortText();
        String clientId2 = testChannel.id().asShortText();

        // 验证 clientId 不为空且一致
        assertNotNull(clientId1, "ClientId 不应为空");
        assertEquals(clientId1, clientId2, "相同通道的ClientId应该一致");

        System.out.println("ClientId 一致性测试通过: " + clientId1);
    }

    /**
     * 测试连接键值生成的一致性
     */
    @Test
    void testConnectionKeyGeneration() {
        String host = "example.com";
        int port = 80;
        String protocol = "TCP";
        String clientId = testChannel.id().asShortText();
        
        // 使用工具类生成连接键值
        String connectionKey1 = ConnectionKeyUtils.generateConnectionKey(host, port, protocol, clientId);
        String connectionKey2 = ConnectionKeyUtils.generateConnectionKey(host, port, protocol, clientId);
        
        // 验证相同参数生成相同键值
        assertEquals(connectionKey1, connectionKey2, "相同参数应生成相同的连接键值");
        
        // 验证键值格式
        String expectedKey = host + ":" + port + ":" + protocol + ":" + clientId;
        assertEquals(expectedKey, connectionKey1, "连接键值格式应正确");
        
        System.out.println("连接键值生成测试通过: " + connectionKey1);
    }

    /**
     * 测试队列索引计算的一致性
     */
    @Test
    void testQueueIndexConsistency() {
        String host = "example.com";
        int port = 80;
        String protocol = "TCP";
        String clientId = testChannel.id().asShortText();
        int queueCount = 4;
        
        // 创建 ProxyRequest
        ProxyRequest request1 = ProxyRequest.builder()
                .target(host, port)
                .protocol(protocol)
                .clientChannel(testChannel)
                .clientId(clientId)
                .build();
        
        ProxyRequest request2 = ProxyRequest.builder()
                .target(host, port)
                .protocol(protocol)
                .clientChannel(testChannel)
                .clientId(clientId)
                .build();
        
        // 计算队列索引
        int queueIndex1 = ConnectionKeyUtils.calculateQueueIndex(request1, queueCount);
        int queueIndex2 = ConnectionKeyUtils.calculateQueueIndex(request2, queueCount);
        
        // 验证相同连接参数的请求路由到同一队列
        assertEquals(queueIndex1, queueIndex2, "相同连接参数的请求应路由到同一队列");
        assertTrue(queueIndex1 >= 0 && queueIndex1 < queueCount, "队列索引应在有效范围内");
        
        System.out.println("队列索引一致性测试通过: queueIndex=" + queueIndex1);
    }

    /**
     * 测试不同连接参数生成不同键值
     */
    @Test
    void testDifferentConnectionParameters() {
        String clientId = testChannel.id().asShortText();
        
        // 不同主机
        String key1 = ConnectionKeyUtils.generateConnectionKey("host1.com", 80, "TCP", clientId);
        String key2 = ConnectionKeyUtils.generateConnectionKey("host2.com", 80, "TCP", clientId);
        assertNotEquals(key1, key2, "不同主机应生成不同的连接键值");
        
        // 不同端口
        String key3 = ConnectionKeyUtils.generateConnectionKey("example.com", 80, "TCP", clientId);
        String key4 = ConnectionKeyUtils.generateConnectionKey("example.com", 443, "TCP", clientId);
        assertNotEquals(key3, key4, "不同端口应生成不同的连接键值");
        
        // 不同协议
        String key5 = ConnectionKeyUtils.generateConnectionKey("example.com", 80, "TCP", clientId);
        String key6 = ConnectionKeyUtils.generateConnectionKey("example.com", 80, "UDP", clientId);
        assertNotEquals(key5, key6, "不同协议应生成不同的连接键值");
        
        // 不同客户端ID
        String key7 = ConnectionKeyUtils.generateConnectionKey("example.com", 80, "TCP", "client1");
        String key8 = ConnectionKeyUtils.generateConnectionKey("example.com", 80, "TCP", "client2");
        assertNotEquals(key7, key8, "不同客户端ID应生成不同的连接键值");
        
        System.out.println("不同连接参数测试通过");
    }

    /**
     * 测试连接键值解析功能
     */
    @Test
    void testConnectionKeyParsing() {
        String host = "example.com";
        int port = 8080;
        String protocol = "UDP";
        String clientId = "test-client-123";
        
        // 生成连接键值
        String connectionKey = ConnectionKeyUtils.generateConnectionKey(host, port, protocol, clientId);
        
        // 解析连接键值
        String[] parts = ConnectionKeyUtils.parseConnectionKey(connectionKey);
        
        // 验证解析结果
        assertEquals(4, parts.length, "解析结果应包含4个部分");
        assertEquals(host, parts[0], "主机名解析错误");
        assertEquals(String.valueOf(port), parts[1], "端口解析错误");
        assertEquals(protocol, parts[2], "协议解析错误");
        assertEquals(clientId, parts[3], "客户端ID解析错误");
        
        System.out.println("连接键值解析测试通过: " + connectionKey);
    }

    /**
     * 测试连接键值验证功能
     */
    @Test
    void testConnectionKeyValidation() {
        // 有效的连接键值
        String validKey = "example.com:80:TCP:client123";
        assertTrue(ConnectionKeyUtils.isValidConnectionKey(validKey), "有效键值应通过验证");
        
        // 无效的连接键值
        assertFalse(ConnectionKeyUtils.isValidConnectionKey(null), "null应验证失败");
        assertFalse(ConnectionKeyUtils.isValidConnectionKey(""), "空字符串应验证失败");
        assertFalse(ConnectionKeyUtils.isValidConnectionKey("invalid"), "格式错误应验证失败");
        assertFalse(ConnectionKeyUtils.isValidConnectionKey("host:port:protocol"), "缺少部分应验证失败");
        assertFalse(ConnectionKeyUtils.isValidConnectionKey("host:abc:TCP:client"), "端口非数字应验证失败");
        
        System.out.println("连接键值验证测试通过");
    }

    /**
     * 测试哈希计算的稳定性
     */
    @Test
    void testHashCalculationStability() {
        String connectionKey = "example.com:80:TCP:client123";
        
        // 多次计算哈希值
        int hash1 = ConnectionKeyUtils.calculateHash(connectionKey);
        int hash2 = ConnectionKeyUtils.calculateHash(connectionKey);
        int hash3 = ConnectionKeyUtils.calculateHash(connectionKey);
        
        // 验证哈希值稳定性
        assertEquals(hash1, hash2, "相同键值的哈希值应相同");
        assertEquals(hash2, hash3, "相同键值的哈希值应相同");
        assertTrue(hash1 >= 0, "哈希值应为非负数");
        
        System.out.println("哈希计算稳定性测试通过: hash=" + hash1);
    }

    /**
     * 测试队列分布的均匀性
     */
    @Test
    void testQueueDistribution() {
        int queueCount = 4;
        int[] queueCounts = new int[queueCount];
        String clientId = testChannel.id().asShortText();
        
        // 生成100个不同的连接键值并统计队列分布
        for (int i = 0; i < 100; i++) {
            String host = "host" + i + ".example.com";
            int port = 80 + (i % 10);
            String protocol = (i % 2 == 0) ? "TCP" : "UDP";
            
            ProxyRequest request = ProxyRequest.builder()
                    .target(host, port)
                    .protocol(protocol)
                    .clientChannel(testChannel)
                    .clientId(clientId)
                    .build();
            
            int queueIndex = ConnectionKeyUtils.calculateQueueIndex(request, queueCount);
            queueCounts[queueIndex]++;
        }
        
        // 验证每个队列都有请求分配
        for (int i = 0; i < queueCount; i++) {
            assertTrue(queueCounts[i] > 0, "队列 " + i + " 应该有请求分配");
            System.out.println("队列 " + i + " 请求数: " + queueCounts[i]);
        }
        
        System.out.println("队列分布均匀性测试通过");
    }
}
