package com.xiang.proxy.server.outbound;

import io.netty.channel.embedded.EmbeddedChannel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 连接状态转换测试
 * 验证修复后的状态转换规则是否正确工作
 */
public class ConnectionStateTransitionTest {

    private EmbeddedChannel testChannel;

    @BeforeEach
    void setUp() {
        testChannel = new EmbeddedChannel();
    }

    /**
     * 测试从 CREATING 状态可以转换到 CLOSING 状态
     * 这是修复的关键场景：在连接创建过程中取消连接
     */
    @Test
    void testCreatingToClosingTransition() {
        // 创建一个测试连接
        OutboundConnection connection = OutboundConnection.builder()
                .backendChannel(testChannel)
                .target("example.com", 80)
                .protocol("TCP")
                .build();

        // 验证初始状态
        assertEquals(ConnectionState.CREATING, connection.getConnectionState());
        assertTrue(connection.isCreating(), "新连接应该处于创建中状态");

        // 尝试标记为正在关闭（这在修复前会失败）
        boolean result = connection.markClosing();
        assertTrue(result, "应该能够从 CREATING 状态转换到 CLOSING 状态");
        assertEquals(ConnectionState.CLOSING, connection.getConnectionState());
        assertFalse(connection.isReusable(), "正在关闭的连接不应该可复用");

        System.out.println("CREATING -> CLOSING 状态转换测试通过");
    }

    /**
     * 测试从 CREATING 状态可以转换到 CLOSED 状态
     * 这是另一个修复的场景：直接关闭创建中的连接
     */
    @Test
    void testCreatingToClosedTransition() {
        // 创建一个测试连接
        OutboundConnection connection = OutboundConnection.builder()
                .backendChannel(testChannel)
                .target("example.com", 80)
                .protocol("TCP")
                .build();

        // 验证初始状态
        assertEquals(ConnectionState.CREATING, connection.getConnectionState());

        // 尝试直接标记为已关闭（这在修复前会失败）
        boolean result = connection.markClosed();
        assertTrue(result, "应该能够从 CREATING 状态转换到 CLOSED 状态");
        assertEquals(ConnectionState.CLOSED, connection.getConnectionState());
        assertTrue(connection.isTerminated(), "已关闭的连接应该处于终态");
        assertFalse(connection.isReusable(), "终态连接不应该可复用");

        System.out.println("CREATING -> CLOSED 状态转换测试通过");
    }

    /**
     * 测试 ConnectionStateHelper 的安全关闭功能
     */
    @Test
    void testSafeCloseConnection() {
        // 测试关闭创建中的连接
        OutboundConnection creatingConnection = OutboundConnection.builder()
                .backendChannel(new EmbeddedChannel())
                .target("example.com", 80)
                .protocol("TCP")
                .build();

        assertEquals(ConnectionState.CREATING, creatingConnection.getConnectionState());
        
        boolean result = ConnectionStateHelper.safeCloseConnection(creatingConnection, "测试关闭");
        assertTrue(result, "应该能够安全关闭创建中的连接");
        assertEquals(ConnectionState.FAILED, creatingConnection.getConnectionState());

        // 测试关闭已连接的连接
        OutboundConnection connectedConnection = OutboundConnection.builder()
                .backendChannel(new EmbeddedChannel())
                .target("example.com", 80)
                .protocol("TCP")
                .build();

        connectedConnection.markConnected();
        assertEquals(ConnectionState.CONNECTED, connectedConnection.getConnectionState());

        result = ConnectionStateHelper.safeCloseConnection(connectedConnection, "测试关闭");
        assertTrue(result, "应该能够安全关闭已连接的连接");
        assertEquals(ConnectionState.CLOSING, connectedConnection.getConnectionState());

        System.out.println("安全关闭连接测试通过");
    }

    /**
     * 测试连接可用性检查
     */
    @Test
    void testConnectionUsabilityCheck() {
        // 创建一个测试连接
        OutboundConnection connection = OutboundConnection.builder()
                .backendChannel(testChannel)
                .target("example.com", 80)
                .protocol("TCP")
                .build();

        // CREATING 状态在未超时时应该可用
        boolean usable = ConnectionStateHelper.isConnectionUsableForSending(connection, 30000);
        assertTrue(usable, "创建中且未超时的连接应该可用于发送数据");

        // 模拟超时
        try {
            java.lang.reflect.Field stateChangeTimeField = OutboundConnection.class.getDeclaredField("stateChangeTime");
            stateChangeTimeField.setAccessible(true);
            long oldTime = System.currentTimeMillis() - 35000; // 35秒前
            stateChangeTimeField.set(connection, oldTime);
        } catch (Exception e) {
            System.out.println("无法修改状态变更时间，跳过超时测试: " + e.getMessage());
            return;
        }

        // 超时后应该不可用，并且连接状态应该被标记为失败
        usable = ConnectionStateHelper.isConnectionUsableForSending(connection, 30000);
        assertFalse(usable, "超时的连接不应该可用于发送数据");
        assertEquals(ConnectionState.FAILED, connection.getConnectionState());

        System.out.println("连接可用性检查测试通过");
    }

    /**
     * 测试状态转换验证
     */
    @Test
    void testStateTransitionValidation() {
        OutboundConnection connection = OutboundConnection.builder()
                .backendChannel(testChannel)
                .target("example.com", 80)
                .protocol("TCP")
                .build();

        // 测试合法的状态转换
        assertTrue(ConnectionStateHelper.validateStateTransition(connection, ConnectionState.CONNECTED, "测试"),
                  "CREATING -> CONNECTED 应该是合法的转换");
        
        assertTrue(ConnectionStateHelper.validateStateTransition(connection, ConnectionState.CLOSING, "测试"),
                  "CREATING -> CLOSING 应该是合法的转换");
        
        assertTrue(ConnectionStateHelper.validateStateTransition(connection, ConnectionState.CLOSED, "测试"),
                  "CREATING -> CLOSED 应该是合法的转换");
        
        assertTrue(ConnectionStateHelper.validateStateTransition(connection, ConnectionState.FAILED, "测试"),
                  "CREATING -> FAILED 应该是合法的转换");

        // 转换到已连接状态
        connection.markConnected();
        
        // 测试从 CONNECTED 状态的转换
        assertTrue(ConnectionStateHelper.validateStateTransition(connection, ConnectionState.CLOSING, "测试"),
                  "CONNECTED -> CLOSING 应该是合法的转换");
        
        assertFalse(ConnectionStateHelper.validateStateTransition(connection, ConnectionState.CREATING, "测试"),
                   "CONNECTED -> CREATING 不应该是合法的转换");

        System.out.println("状态转换验证测试通过");
    }

    /**
     * 测试连接状态信息获取
     */
    @Test
    void testConnectionStateInfo() {
        OutboundConnection connection = OutboundConnection.builder()
                .backendChannel(testChannel)
                .target("example.com", 80)
                .protocol("TCP")
                .build();

        String info = ConnectionStateHelper.getConnectionStateInfo(connection);
        assertNotNull(info, "状态信息不应该为空");
        assertTrue(info.contains("connectionId="), "状态信息应该包含连接ID");
        assertTrue(info.contains("state=CREATING"), "状态信息应该包含当前状态");
        assertTrue(info.contains("reusable=false"), "状态信息应该包含复用状态");

        System.out.println("连接状态信息: " + info);
        System.out.println("连接状态信息获取测试通过");
    }

    /**
     * 测试批量连接清理功能
     */
    @Test
    void testBatchConnectionCleanup() {
        java.util.Map<String, OutboundConnection> connections = new java.util.concurrent.ConcurrentHashMap<>();
        
        // 添加一些测试连接
        OutboundConnection normalConnection = OutboundConnection.builder()
                .backendChannel(new EmbeddedChannel())
                .target("example.com", 80)
                .protocol("TCP")
                .build();
        normalConnection.markConnected();
        connections.put("normal", normalConnection);

        OutboundConnection failedConnection = OutboundConnection.builder()
                .backendChannel(new EmbeddedChannel())
                .target("example.com", 80)
                .protocol("TCP")
                .build();
        failedConnection.markFailed();
        connections.put("failed", failedConnection);

        // 执行清理
        int cleanupCount = ConnectionStateHelper.cleanupInvalidConnections(connections, 30000);
        
        // 验证结果
        assertTrue(cleanupCount >= 0, "清理数量应该大于等于0");
        assertTrue(connections.containsKey("normal"), "正常连接应该保留");
        
        System.out.println("批量清理连接数量: " + cleanupCount);
        System.out.println("剩余连接数量: " + connections.size());
        System.out.println("批量连接清理测试通过");
    }
}
