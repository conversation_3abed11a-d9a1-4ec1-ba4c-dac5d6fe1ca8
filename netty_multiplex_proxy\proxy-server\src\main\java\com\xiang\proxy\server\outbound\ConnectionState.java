package com.xiang.proxy.server.outbound;

/**
 * 出站连接状态枚举
 * 定义连接的生命周期状态，便于精确管理连接状态
 */
public enum ConnectionState {
    
    /**
     * 连接正在创建中
     * - 连接请求已发起但尚未完成
     * - 此状态下连接不可复用，但可以等待连接完成
     */
    CREATING("正在创建", false, true),
    
    /**
     * 连接已建立且可用
     * - 连接成功建立，通道活跃且可写
     * - 此状态下连接可以复用
     */
    CONNECTED("已连接", true, true),
    
    /**
     * 连接正在关闭中
     * - 连接关闭请求已发起但尚未完成
     * - 此状态下连接不可复用
     */
    CLOSING("正在关闭", false, false),
    
    /**
     * 连接已关闭
     * - 连接已正常关闭
     * - 此状态下连接不可复用且应被清理
     */
    CLOSED("已关闭", false, false),
    
    /**
     * 连接创建失败
     * - 连接建立过程中发生错误
     * - 此状态下连接不可复用且应被清理
     */
    FAILED("创建失败", false, false),
    
    /**
     * 连接发生错误
     * - 连接在使用过程中发生错误
     * - 此状态下连接不可复用且应被清理
     */
    ERROR("发生错误", false, false);
    
    private final String description;
    private final boolean reusable;      // 是否可复用
    private final boolean valid;         // 是否有效（未失效）
    
    ConnectionState(String description, boolean reusable, boolean valid) {
        this.description = description;
        this.reusable = reusable;
        this.valid = valid;
    }
    
    /**
     * 获取状态描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 连接是否可复用
     * 只有 CONNECTED 状态的连接才可复用
     */
    public boolean isReusable() {
        return reusable;
    }
    
    /**
     * 连接是否有效（未失效）
     * CREATING 和 CONNECTED 状态的连接被认为是有效的
     */
    public boolean isValid() {
        return valid;
    }
    
    /**
     * 连接是否已完成（成功或失败）
     */
    public boolean isCompleted() {
        return this != CREATING && this != CLOSING;
    }
    
    /**
     * 连接是否处于终态
     * 终态包括：CLOSED, FAILED, ERROR
     */
    public boolean isTerminal() {
        return this == CLOSED || this == FAILED || this == ERROR;
    }
    
    /**
     * 检查状态转换是否合法
     */
    public boolean canTransitionTo(ConnectionState newState) {
        if (newState == null) {
            return false;
        }
        
        // 已经是终态的连接不能再转换状态
        if (this.isTerminal()) {
            return false;
        }
        
        switch (this) {
            case CREATING:
                // 创建中可以转换为：已连接、创建失败、发生错误、正在关闭、已关闭
                // 允许在创建过程中取消或关闭连接
                return newState == CONNECTED || newState == FAILED || newState == ERROR ||
                       newState == CLOSING || newState == CLOSED;

            case CONNECTED:
                // 已连接可以转换为：正在关闭、发生错误
                return newState == CLOSING || newState == ERROR;

            case CLOSING:
                // 正在关闭可以转换为：已关闭、发生错误
                return newState == CLOSED || newState == ERROR;

            default:
                return false;
        }
    }
    
    /**
     * 获取状态转换的描述信息
     */
    public String getTransitionDescription(ConnectionState newState) {
        if (!canTransitionTo(newState)) {
            return String.format("非法状态转换: %s -> %s", this.description, 
                                newState != null ? newState.description : "null");
        }
        
        return String.format("状态转换: %s -> %s", this.description, newState.description);
    }
    
    @Override
    public String toString() {
        return String.format("%s(%s)", name(), description);
    }
}
