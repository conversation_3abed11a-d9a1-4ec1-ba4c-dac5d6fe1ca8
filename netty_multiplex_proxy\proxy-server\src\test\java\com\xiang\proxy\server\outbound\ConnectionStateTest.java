package com.xiang.proxy.server.outbound;

import io.netty.channel.embedded.EmbeddedChannel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 连接状态管理测试
 * 验证 ConnectionState 枚举和 OutboundConnection 状态转换的正确性
 */
public class ConnectionStateTest {

    private OutboundConnection connection;

    @BeforeEach
    void setUp() {
        EmbeddedChannel testChannel = new EmbeddedChannel();
        
        connection = OutboundConnection.builder()
                .backendChannel(testChannel)
                .target("example.com", 80)
                .protocol("TCP")
                .build();
    }

    /**
     * 测试连接状态枚举的基本属性
     */
    @Test
    void testConnectionStateProperties() {
        // 测试 CREATING 状态
        ConnectionState creating = ConnectionState.CREATING;
        assertFalse(creating.isReusable(), "CREATING 状态不应可复用");
        assertTrue(creating.isValid(), "CREATING 状态应该有效");
        assertFalse(creating.isCompleted(), "CREATING 状态未完成");
        assertFalse(creating.isTerminal(), "CREATING 状态不是终态");

        // 测试 CONNECTED 状态
        ConnectionState connected = ConnectionState.CONNECTED;
        assertTrue(connected.isReusable(), "CONNECTED 状态应可复用");
        assertTrue(connected.isValid(), "CONNECTED 状态应该有效");
        assertTrue(connected.isCompleted(), "CONNECTED 状态已完成");
        assertFalse(connected.isTerminal(), "CONNECTED 状态不是终态");

        // 测试 FAILED 状态
        ConnectionState failed = ConnectionState.FAILED;
        assertFalse(failed.isReusable(), "FAILED 状态不应可复用");
        assertFalse(failed.isValid(), "FAILED 状态无效");
        assertTrue(failed.isCompleted(), "FAILED 状态已完成");
        assertTrue(failed.isTerminal(), "FAILED 状态是终态");

        // 测试 CLOSED 状态
        ConnectionState closed = ConnectionState.CLOSED;
        assertFalse(closed.isReusable(), "CLOSED 状态不应可复用");
        assertFalse(closed.isValid(), "CLOSED 状态无效");
        assertTrue(closed.isCompleted(), "CLOSED 状态已完成");
        assertTrue(closed.isTerminal(), "CLOSED 状态是终态");

        System.out.println("连接状态属性测试通过");
    }

    /**
     * 测试合法的状态转换
     */
    @Test
    void testValidStateTransitions() {
        // CREATING -> CONNECTED
        assertTrue(ConnectionState.CREATING.canTransitionTo(ConnectionState.CONNECTED),
                "CREATING 应该可以转换为 CONNECTED");

        // CREATING -> FAILED
        assertTrue(ConnectionState.CREATING.canTransitionTo(ConnectionState.FAILED),
                "CREATING 应该可以转换为 FAILED");

        // CONNECTED -> CLOSING
        assertTrue(ConnectionState.CONNECTED.canTransitionTo(ConnectionState.CLOSING),
                "CONNECTED 应该可以转换为 CLOSING");

        // CONNECTED -> ERROR
        assertTrue(ConnectionState.CONNECTED.canTransitionTo(ConnectionState.ERROR),
                "CONNECTED 应该可以转换为 ERROR");

        // CLOSING -> CLOSED
        assertTrue(ConnectionState.CLOSING.canTransitionTo(ConnectionState.CLOSED),
                "CLOSING 应该可以转换为 CLOSED");

        // CLOSING -> ERROR
        assertTrue(ConnectionState.CLOSING.canTransitionTo(ConnectionState.ERROR),
                "CLOSING 应该可以转换为 ERROR");

        System.out.println("合法状态转换测试通过");
    }

    /**
     * 测试非法的状态转换
     */
    @Test
    void testInvalidStateTransitions() {
        // 终态不能转换为其他状态
        assertFalse(ConnectionState.FAILED.canTransitionTo(ConnectionState.CONNECTED),
                "FAILED 不应该可以转换为 CONNECTED");
        assertFalse(ConnectionState.CLOSED.canTransitionTo(ConnectionState.CREATING),
                "CLOSED 不应该可以转换为 CREATING");
        assertFalse(ConnectionState.ERROR.canTransitionTo(ConnectionState.CONNECTED),
                "ERROR 不应该可以转换为 CONNECTED");

        // 不合理的状态转换
        assertFalse(ConnectionState.CREATING.canTransitionTo(ConnectionState.CLOSING),
                "CREATING 不应该可以转换为 CLOSING");
        assertFalse(ConnectionState.CONNECTED.canTransitionTo(ConnectionState.CREATING),
                "CONNECTED 不应该可以转换为 CREATING");

        System.out.println("非法状态转换测试通过");
    }

    /**
     * 测试 OutboundConnection 的初始状态
     */
    @Test
    void testOutboundConnectionInitialState() {
        assertEquals(ConnectionState.CREATING, connection.getConnectionState(),
                "新创建的连接应该处于 CREATING 状态");
        assertFalse(connection.isReusable(), "CREATING 状态的连接不应可复用");
        assertTrue(connection.isCreating(), "连接应该处于创建中状态");
        assertFalse(connection.isConnected(), "连接不应该处于已连接状态");
        assertFalse(connection.isTerminated(), "连接不应该处于终态");

        System.out.println("OutboundConnection 初始状态测试通过");
    }

    /**
     * 测试 OutboundConnection 状态转换
     */
    @Test
    void testOutboundConnectionStateTransitions() {
        // 初始状态为 CREATING
        assertEquals(ConnectionState.CREATING, connection.getConnectionState());

        // 转换为 CONNECTED
        assertTrue(connection.markConnected(), "应该能够标记为已连接");
        assertEquals(ConnectionState.CONNECTED, connection.getConnectionState());
        assertTrue(connection.isConnected(), "连接应该处于已连接状态");
        assertTrue(connection.isReusable(), "已连接的连接应该可复用");

        // 转换为 ERROR
        assertTrue(connection.markError(), "应该能够标记为错误");
        assertEquals(ConnectionState.ERROR, connection.getConnectionState());
        assertFalse(connection.isReusable(), "错误状态的连接不应可复用");
        assertTrue(connection.isTerminated(), "错误状态的连接应该处于终态");

        // 尝试从终态转换（应该失败）
        assertFalse(connection.markConnected(), "不应该能从终态转换为其他状态");
        assertEquals(ConnectionState.ERROR, connection.getConnectionState(),
                "状态应该保持不变");

        System.out.println("OutboundConnection 状态转换测试通过");
    }

    /**
     * 测试状态转换的时间戳更新
     */
    @Test
    void testStateChangeTimestamp() {
        long initialTime = connection.getStateChangeTime();
        
        // 等待一小段时间确保时间戳不同
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 转换状态
        assertTrue(connection.markConnected());
        long afterTransitionTime = connection.getStateChangeTime();

        assertTrue(afterTransitionTime > initialTime,
                "状态转换后时间戳应该更新");

        System.out.println("状态转换时间戳测试通过");
    }

    /**
     * 测试连接复用判断
     */
    @Test
    void testConnectionReusability() {
        // 初始状态不可复用
        assertFalse(connection.isReusable(), "CREATING 状态不可复用");

        // 标记为已连接后可复用
        connection.markConnected();
        assertTrue(connection.isReusable(), "CONNECTED 状态且通道活跃时可复用");

        // 标记为错误后不可复用
        connection.markError();
        assertFalse(connection.isReusable(), "ERROR 状态不可复用");

        System.out.println("连接复用判断测试通过");
    }

    /**
     * 测试状态描述信息
     */
    @Test
    void testStateDescriptions() {
        assertEquals("正在创建", ConnectionState.CREATING.getDescription());
        assertEquals("已连接", ConnectionState.CONNECTED.getDescription());
        assertEquals("正在关闭", ConnectionState.CLOSING.getDescription());
        assertEquals("已关闭", ConnectionState.CLOSED.getDescription());
        assertEquals("创建失败", ConnectionState.FAILED.getDescription());
        assertEquals("发生错误", ConnectionState.ERROR.getDescription());

        // 测试状态转换描述
        String transitionDesc = ConnectionState.CREATING.getTransitionDescription(ConnectionState.CONNECTED);
        assertTrue(transitionDesc.contains("正在创建") && transitionDesc.contains("已连接"),
                "状态转换描述应该包含源状态和目标状态");

        System.out.println("状态描述信息测试通过");
    }

    /**
     * 测试向后兼容的 markInactive 方法
     */
    @Test
    void testBackwardCompatibility() {
        // 使用已弃用的 markInactive 方法
        connection.markInactive();
        
        // 应该转换为 ERROR 状态
        assertEquals(ConnectionState.ERROR, connection.getConnectionState(),
                "markInactive 应该转换为 ERROR 状态");
        assertTrue(connection.isTerminated(), "应该处于终态");

        System.out.println("向后兼容性测试通过");
    }
}
