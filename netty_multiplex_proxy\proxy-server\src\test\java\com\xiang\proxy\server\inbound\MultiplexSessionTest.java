package com.xiang.proxy.server.inbound;

import com.xiang.proxy.server.core.ProxyProcessor;
import com.xiang.proxy.server.core.ProxyRequest;
import com.xiang.proxy.server.inbound.impl.multiplex.MultiplexSession;
import com.xiang.proxy.server.outbound.OutboundConnection;
import com.xiang.proxy.server.protocol.MultiplexProtocol;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.embedded.EmbeddedChannel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.concurrent.ConcurrentHashMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * MultiplexSession测试
 */
public class MultiplexSessionTest {

    @Mock
    private ProxyProcessor mockProxyProcessor;

    @Mock
    private OutboundConnection mockConnection;

    private MultiplexSession multiplexSession;
    private Channel testChannel;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        testChannel = new EmbeddedChannel();
        
        // 配置mock
        when(mockProxyProcessor.getActiveConnection(anyString())).thenReturn(mockConnection);
        when(mockConnection.isActive()).thenReturn(true);
        when(mockConnection.getConnectionId()).thenReturn("test-connection-id");
        
        multiplexSession = new MultiplexSession(testChannel, mockProxyProcessor, 12345L);
    }

    @Test
    void testSessionIdAllocation() {
        try {
            // 使用反射访问私有方法
            java.lang.reflect.Method allocateSessionIdMethod = 
                MultiplexSession.class.getDeclaredMethod("allocateSessionId");
            allocateSessionIdMethod.setAccessible(true);

            // 分配多个会话ID
            int sessionId1 = (Integer) allocateSessionIdMethod.invoke(multiplexSession);
            int sessionId2 = (Integer) allocateSessionIdMethod.invoke(multiplexSession);
            int sessionId3 = (Integer) allocateSessionIdMethod.invoke(multiplexSession);

            assertTrue(sessionId1 > 0 && sessionId1 <= 10000, "会话ID应该在有效范围内");
            assertTrue(sessionId2 > 0 && sessionId2 <= 10000, "会话ID应该在有效范围内");
            assertTrue(sessionId3 > 0 && sessionId3 <= 10000, "会话ID应该在有效范围内");

            assertNotEquals(sessionId1, sessionId2, "不同的会话应该有不同的ID");
            assertNotEquals(sessionId2, sessionId3, "不同的会话应该有不同的ID");

            System.out.println("分配的会话ID: " + sessionId1 + ", " + sessionId2 + ", " + sessionId3);
        } catch (Exception e) {
            fail("会话ID分配测试失败: " + e.getMessage());
        }
    }

    @Test
    void testSessionIdReuse() {
        try {
            // 获取私有字段和方法
            java.lang.reflect.Field sessionConnectionsField = 
                MultiplexSession.class.getDeclaredField("sessionConnections");
            sessionConnectionsField.setAccessible(true);
            @SuppressWarnings("unchecked")
            ConcurrentHashMap<Integer, String> sessionConnections = 
                (ConcurrentHashMap<Integer, String>) sessionConnectionsField.get(multiplexSession);

            java.lang.reflect.Field reusableSessionIdsField = 
                MultiplexSession.class.getDeclaredField("reusableSessionIds");
            reusableSessionIdsField.setAccessible(true);
            @SuppressWarnings("unchecked")
            java.util.Queue<Integer> reusableSessionIds = 
                (java.util.Queue<Integer>) reusableSessionIdsField.get(multiplexSession);

            java.lang.reflect.Method allocateSessionIdMethod = 
                MultiplexSession.class.getDeclaredMethod("allocateSessionId");
            allocateSessionIdMethod.setAccessible(true);

            // 分配一个会话ID
            int sessionId = (Integer) allocateSessionIdMethod.invoke(multiplexSession);
            sessionConnections.put(sessionId, "test-connection");

            // 模拟会话关闭，将ID加入重用队列
            sessionConnections.remove(sessionId);
            reusableSessionIds.offer(sessionId);

            // 再次分配会话ID，应该重用之前的ID
            int reusedSessionId = (Integer) allocateSessionIdMethod.invoke(multiplexSession);
            assertEquals(sessionId, reusedSessionId, "应该重用之前释放的会话ID");

        } catch (Exception e) {
            fail("会话ID重用测试失败: " + e.getMessage());
        }
    }

    @Test
    void testHostKeyParsing() {
        // 测试主机键值解析
        String testHostKey = "example.com:80:TCP:client-001";
        
        try {
            // 模拟handleDataPacket中的解析逻辑
            String[] hostKeyParts = testHostKey.split(":");
            
            assertEquals(4, hostKeyParts.length, "主机键值应该包含4个部分");
            assertEquals("example.com", hostKeyParts[0], "主机名解析错误");
            assertEquals("80", hostKeyParts[1], "端口号解析错误");
            assertEquals("TCP", hostKeyParts[2], "协议解析错误");
            assertEquals("client-001", hostKeyParts[3], "客户端ID解析错误");

            int port = Integer.parseInt(hostKeyParts[1]);
            assertEquals(80, port, "端口号转换错误");

        } catch (Exception e) {
            fail("主机键值解析测试失败: " + e.getMessage());
        }
    }

    @Test
    void testDataPacketProcessing() {
        try {
            // 设置会话映射
            java.lang.reflect.Field sessionConnectionsField = 
                MultiplexSession.class.getDeclaredField("sessionConnections");
            sessionConnectionsField.setAccessible(true);
            @SuppressWarnings("unchecked")
            ConcurrentHashMap<Integer, String> sessionConnections = 
                (ConcurrentHashMap<Integer, String>) sessionConnectionsField.get(multiplexSession);

            java.lang.reflect.Field sessionHostKeysField = 
                MultiplexSession.class.getDeclaredField("sessionHostKeys");
            sessionHostKeysField.setAccessible(true);
            @SuppressWarnings("unchecked")
            ConcurrentHashMap<Integer, String> sessionHostKeys = 
                (ConcurrentHashMap<Integer, String>) sessionHostKeysField.get(multiplexSession);

            // 设置测试数据
            int testSessionId = 1;
            String testConnectionId = "test-connection-id";
            String testHostKey = "example.com:80:TCP:client-001";

            sessionConnections.put(testSessionId, testConnectionId);
            sessionHostKeys.put(testSessionId, testHostKey);

            // 创建测试数据包
            byte[] testData = "Hello, World!".getBytes();
            ByteBuf dataBuf = Unpooled.copiedBuffer(testData);
            MultiplexProtocol.Packet dataPacket = MultiplexProtocol.createDataPacket(testSessionId, testData);

            // 获取私有方法
            java.lang.reflect.Method handleDataPacketMethod = 
                MultiplexSession.class.getDeclaredMethod("handleDataPacket", MultiplexProtocol.Packet.class);
            handleDataPacketMethod.setAccessible(true);

            // 执行数据包处理（这里主要测试不抛异常）
            assertDoesNotThrow(() -> {
                try {
                    handleDataPacketMethod.invoke(multiplexSession, dataPacket);
                } catch (Exception e) {
                    if (e.getCause() instanceof RuntimeException) {
                        throw (RuntimeException) e.getCause();
                    }
                    throw new RuntimeException(e);
                }
            }, "数据包处理不应该抛出异常");

        } catch (Exception e) {
            fail("数据包处理测试失败: " + e.getMessage());
        }
    }

    @Test
    void testTerminatedSessionsCleanup() {
        try {
            // 获取私有字段
            java.lang.reflect.Field terminatedSessionsField = 
                MultiplexSession.class.getDeclaredField("terminatedSessions");
            terminatedSessionsField.setAccessible(true);
            @SuppressWarnings("unchecked")
            ConcurrentHashMap<Integer, Long> terminatedSessions = 
                (ConcurrentHashMap<Integer, Long>) terminatedSessionsField.get(multiplexSession);

            // 添加一些过期的终止会话记录
            long now = System.currentTimeMillis();
            terminatedSessions.put(1, now - 120000); // 2分钟前
            terminatedSessions.put(2, now - 30000);  // 30秒前
            terminatedSessions.put(3, now - 5000);   // 5秒前

            assertEquals(3, terminatedSessions.size(), "应该有3个终止会话记录");

            // 获取清理方法
            java.lang.reflect.Method cleanupMethod = 
                MultiplexSession.class.getDeclaredMethod("cleanupExpiredTerminatedSessions");
            cleanupMethod.setAccessible(true);

            // 执行清理
            cleanupMethod.invoke(multiplexSession);

            // 验证过期记录被清理（假设CLOSED_SESSION_TTL_MS为60秒）
            assertTrue(terminatedSessions.size() <= 3, "过期的终止会话记录应该被清理");

        } catch (Exception e) {
            fail("终止会话清理测试失败: " + e.getMessage());
        }
    }

    @Test
    void testConnectionValidation() {
        try {
            // 获取私有字段
            java.lang.reflect.Field sessionConnectionsField = 
                MultiplexSession.class.getDeclaredField("sessionConnections");
            sessionConnectionsField.setAccessible(true);
            @SuppressWarnings("unchecked")
            ConcurrentHashMap<Integer, String> sessionConnections = 
                (ConcurrentHashMap<Integer, String>) sessionConnectionsField.get(multiplexSession);

            java.lang.reflect.Field sessionHostKeysField = 
                MultiplexSession.class.getDeclaredField("sessionHostKeys");
            sessionHostKeysField.setAccessible(true);
            @SuppressWarnings("unchecked")
            ConcurrentHashMap<Integer, String> sessionHostKeys = 
                (ConcurrentHashMap<Integer, String>) sessionHostKeysField.get(multiplexSession);

            // 添加测试会话
            sessionConnections.put(1, "valid-connection");
            sessionConnections.put(2, "invalid-connection");
            sessionHostKeys.put(1, "example1.com:80:TCP:client-001");
            sessionHostKeys.put(2, "example2.com:80:TCP:client-002");

            // 配置mock：第一个连接有效，第二个无效
            when(mockProxyProcessor.getActiveConnection("valid-connection")).thenReturn(mockConnection);
            when(mockProxyProcessor.getActiveConnection("invalid-connection")).thenReturn(null);

            assertEquals(2, sessionConnections.size(), "应该有2个活跃会话");

            // 获取验证方法
            java.lang.reflect.Method validateMethod = 
                MultiplexSession.class.getDeclaredMethod("validateActiveSessions");
            validateMethod.setAccessible(true);

            // 执行验证
            validateMethod.invoke(multiplexSession);

            // 验证无效会话被清理
            assertTrue(sessionConnections.size() <= 2, "无效会话应该被清理");
            assertFalse(sessionConnections.containsKey(2) && sessionHostKeys.containsKey(2), 
                       "无效会话的映射应该被清理");

        } catch (Exception e) {
            fail("连接验证测试失败: " + e.getMessage());
        }
    }
}
