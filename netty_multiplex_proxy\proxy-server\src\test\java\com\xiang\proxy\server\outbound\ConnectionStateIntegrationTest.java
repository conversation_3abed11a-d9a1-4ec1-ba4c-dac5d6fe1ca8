package com.xiang.proxy.server.outbound;

import io.netty.channel.embedded.EmbeddedChannel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 连接状态管理集成测试
 * 验证状态管理在实际使用场景中的效果
 *
 * 注意：此测试专注于测试 OutboundConnection 的状态管理功能
 */
public class ConnectionStateIntegrationTest {

    private EmbeddedChannel testChannel;

    @BeforeEach
    void setUp() {
        testChannel = new EmbeddedChannel();
    }

    /**
     * 测试连接创建过程中的状态转换
     */
    @Test
    void testConnectionCreationStateTransition() throws Exception {
        // 创建一个测试连接
        OutboundConnection connection = OutboundConnection.builder()
                .backendChannel(testChannel)
                .target("example.com", 80)
                .protocol("TCP")
                .build();

        // 验证连接初始状态
        assertNotNull(connection, "连接不应为空");
        assertEquals(ConnectionState.CREATING, connection.getConnectionState(),
                    "新创建的连接应该处于 CREATING 状态");

        // 模拟连接成功
        assertTrue(connection.markConnected(), "应该能够标记连接为已连接");
        assertEquals(ConnectionState.CONNECTED, connection.getConnectionState(),
                    "标记后连接应该处于 CONNECTED 状态");

        System.out.println("连接创建状态转换测试通过");
    }

    /**
     * 测试连接状态对复用判断的影响
     */
    @Test
    void testConnectionStateReusability() {
        // 创建一个测试连接
        OutboundConnection connection = OutboundConnection.builder()
                .backendChannel(testChannel)
                .target("example.com", 80)
                .protocol("TCP")
                .build();

        // CREATING 状态不可复用
        assertEquals(ConnectionState.CREATING, connection.getConnectionState());
        assertFalse(connection.isReusable(), "CREATING 状态的连接不应该可复用");
        assertTrue(connection.isCreating(), "连接应该处于创建中状态");

        // CONNECTED 状态可复用
        connection.markConnected();
        assertEquals(ConnectionState.CONNECTED, connection.getConnectionState());
        assertTrue(connection.isReusable(), "CONNECTED 状态的连接应该可复用");
        assertTrue(connection.isConnected(), "连接应该处于已连接状态");

        // ERROR 状态不可复用
        connection.markError();
        assertEquals(ConnectionState.ERROR, connection.getConnectionState());
        assertFalse(connection.isReusable(), "ERROR 状态的连接不应该可复用");
        assertTrue(connection.isTerminated(), "ERROR 状态的连接应该处于终态");

        System.out.println("连接状态复用判断测试通过");
    }

    /**
     * 测试连接关闭时的状态转换
     */
    @Test
    void testConnectionCloseStateTransition() {
        // 创建一个测试连接
        OutboundConnection connection = OutboundConnection.builder()
                .backendChannel(testChannel)
                .target("example.com", 80)
                .protocol("TCP")
                .build();

        // 标记为已连接状态
        connection.markConnected();
        assertEquals(ConnectionState.CONNECTED, connection.getConnectionState());
        assertTrue(connection.isReusable(), "已连接的连接应该可复用");

        // 模拟连接关闭过程
        assertTrue(connection.markClosing(), "应该能够标记连接为正在关闭");
        assertEquals(ConnectionState.CLOSING, connection.getConnectionState());
        assertFalse(connection.isReusable(), "正在关闭的连接不应该可复用");

        // 标记为已关闭
        assertTrue(connection.markClosed(), "应该能够标记连接为已关闭");
        assertEquals(ConnectionState.CLOSED, connection.getConnectionState());
        assertTrue(connection.isTerminated(), "已关闭的连接应该处于终态");
        assertFalse(connection.isReusable(), "终态连接不应该可复用");

        System.out.println("连接关闭状态转换测试通过");
    }

    /**
     * 测试连接状态超时检测
     */
    @Test
    void testConnectionStateTimeout() throws Exception {
        // 创建一个测试连接
        OutboundConnection connection = OutboundConnection.builder()
                .backendChannel(testChannel)
                .target("example.com", 80)
                .protocol("TCP")
                .build();

        // 验证初始状态
        assertEquals(ConnectionState.CREATING, connection.getConnectionState());
        assertTrue(connection.isCreating(), "新连接应该处于创建中状态");

        // 模拟时间流逝（通过反射修改状态变更时间）
        try {
            java.lang.reflect.Field stateChangeTimeField = OutboundConnection.class.getDeclaredField("stateChangeTime");
            stateChangeTimeField.setAccessible(true);
            long oldTime = System.currentTimeMillis() - 35000; // 35秒前
            stateChangeTimeField.set(connection, oldTime);
        } catch (Exception e) {
            System.out.println("无法修改状态变更时间，跳过超时测试: " + e.getMessage());
            return;
        }

        // 现在连接应该被认为是超时的
        long stateAge = System.currentTimeMillis() - connection.getStateChangeTime();
        assertTrue(stateAge > 30000, "状态年龄应该超过30秒");

        // 验证连接仍然处于 CREATING 状态，但年龄已经很大
        assertEquals(ConnectionState.CREATING, connection.getConnectionState());
        assertFalse(connection.isReusable(), "超时的创建中连接不应该可复用");

        // 手动标记为失败（模拟超时检测逻辑）
        assertTrue(connection.markFailed(), "应该能够标记超时连接为失败");
        assertEquals(ConnectionState.FAILED, connection.getConnectionState());
        assertTrue(connection.isTerminated(), "失败的连接应该处于终态");

        System.out.println("连接状态超时检测测试通过");
    }

    /**
     * 测试连接复用判断
     */
    @Test
    void testConnectionReusabilityCheck() {
        // 创建一个测试连接
        OutboundConnection connection = OutboundConnection.builder()
                .backendChannel(testChannel)
                .target("example.com", 80)
                .protocol("TCP")
                .build();

        // CREATING 状态不可复用
        assertEquals(ConnectionState.CREATING, connection.getConnectionState());
        assertFalse(connection.isReusable(), "CREATING 状态的连接不应该可复用");

        // CONNECTED 状态可复用
        connection.markConnected();
        assertEquals(ConnectionState.CONNECTED, connection.getConnectionState());
        assertTrue(connection.isReusable(), "CONNECTED 状态的连接应该可复用");

        // ERROR 状态不可复用
        connection.markError();
        assertEquals(ConnectionState.ERROR, connection.getConnectionState());
        assertFalse(connection.isReusable(), "ERROR 状态的连接不应该可复用");

        // FAILED 状态不可复用
        OutboundConnection connection2 = OutboundConnection.builder()
                .backendChannel(new EmbeddedChannel())
                .target("example.com", 80)
                .protocol("TCP")
                .build();
        
        connection2.markFailed();
        assertEquals(ConnectionState.FAILED, connection2.getConnectionState());
        assertFalse(connection2.isReusable(), "FAILED 状态的连接不应该可复用");

        System.out.println("连接复用判断测试通过");
    }
}
