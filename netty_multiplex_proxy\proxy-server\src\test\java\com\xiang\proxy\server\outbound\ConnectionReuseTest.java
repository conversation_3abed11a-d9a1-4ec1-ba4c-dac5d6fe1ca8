package com.xiang.proxy.server.outbound;

import com.xiang.proxy.server.core.ProxyRequest;
import com.xiang.proxy.server.outbound.impl.TcpDirectOutboundHandler;
import io.netty.channel.Channel;
import io.netty.channel.embedded.EmbeddedChannel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 连接复用机制测试
 */
public class ConnectionReuseTest {

    private TcpDirectOutboundHandler outboundHandler;
    private Channel testChannel;

    @BeforeEach
    void setUp() {
        outboundHandler = new TcpDirectOutboundHandler("test-outbound", new OutboundConfig());
        testChannel = new EmbeddedChannel();
    }

    @Test
    void testConnectionKeyGeneration() {
        // 测试连接键值生成
        ProxyRequest request1 = ProxyRequest.builder()
                .target("example.com", 80)
                .protocol("TCP")
                .clientChannel(testChannel)
                .clientId("client-001")
                .build();

        ProxyRequest request2 = ProxyRequest.builder()
                .target("example.com", 80)
                .protocol("TCP")
                .clientChannel(testChannel)
                .clientId("client-001")
                .build();

        ProxyRequest request3 = ProxyRequest.builder()
                .target("example.com", 80)
                .protocol("TCP")
                .clientChannel(testChannel)
                .clientId("client-002") // 不同的客户端ID
                .build();

        try {
            // 使用反射访问私有方法
            java.lang.reflect.Method getPoolKeyMethod = 
                TcpDirectOutboundHandler.class.getDeclaredMethod("getPoolKey", ProxyRequest.class);
            getPoolKeyMethod.setAccessible(true);

            String key1 = (String) getPoolKeyMethod.invoke(outboundHandler, request1);
            String key2 = (String) getPoolKeyMethod.invoke(outboundHandler, request2);
            String key3 = (String) getPoolKeyMethod.invoke(outboundHandler, request3);

            assertEquals(key1, key2, "相同连接参数的请求应该生成相同的连接键值");
            assertNotEquals(key1, key3, "不同客户端ID的请求应该生成不同的连接键值");

            // 验证键值格式
            assertTrue(key1.contains("example.com"), "连接键值应该包含主机名");
            assertTrue(key1.contains("80"), "连接键值应该包含端口号");
            assertTrue(key1.contains("TCP"), "连接键值应该包含协议");
            assertTrue(key1.contains("client-001"), "连接键值应该包含客户端ID");

            System.out.println("连接键值1: " + key1);
            System.out.println("连接键值2: " + key2);
            System.out.println("连接键值3: " + key3);
        } catch (Exception e) {
            fail("连接键值生成测试失败: " + e.getMessage());
        }
    }

    @Test
    void testConnectionReusabilityCheck() {
        // 测试连接可复用性检查
        try {
            java.lang.reflect.Method isConnectionReusableMethod = 
                TcpDirectOutboundHandler.class.getDeclaredMethod("isConnectionReusable", OutboundConnection.class);
            isConnectionReusableMethod.setAccessible(true);

            // 测试null连接
            boolean result1 = (Boolean) isConnectionReusableMethod.invoke(outboundHandler, (OutboundConnection) null);
            assertFalse(result1, "null连接应该不可复用");

            // 创建一个测试连接
            OutboundConnection testConnection = OutboundConnection.builder()
                    .target("example.com", 80)
                    .protocol("TCP")
                    .createTime(System.currentTimeMillis())
                    .build();

            // 测试没有后端通道的连接
            boolean result2 = (Boolean) isConnectionReusableMethod.invoke(outboundHandler, testConnection);
            assertFalse(result2, "没有后端通道的连接应该不可复用");

            // 测试过旧的连接
            OutboundConnection oldConnection = OutboundConnection.builder()
                    .target("example.com", 80)
                    .protocol("TCP")
                    .createTime(System.currentTimeMillis() - 400000) // 6分钟前创建
                    .build();

            boolean result3 = (Boolean) isConnectionReusableMethod.invoke(outboundHandler, oldConnection);
            assertFalse(result3, "过旧的连接应该不可复用");

        } catch (Exception e) {
            fail("连接可复用性检查测试失败: " + e.getMessage());
        }
    }

    @Test
    void testConnectionCacheCleanup() {
        // 测试连接缓存清理机制
        try {
            // 获取连接缓存
            java.lang.reflect.Field connectionCacheField = 
                TcpDirectOutboundHandler.class.getDeclaredField("connectionCache");
            connectionCacheField.setAccessible(true);
            @SuppressWarnings("unchecked")
            java.util.Map<String, OutboundConnection> connectionCache = 
                (java.util.Map<String, OutboundConnection>) connectionCacheField.get(outboundHandler);

            // 添加一些测试连接到缓存
            OutboundConnection connection1 = OutboundConnection.builder()
                    .target("example1.com", 80)
                    .protocol("TCP")
                    .createTime(System.currentTimeMillis())
                    .build();

            OutboundConnection connection2 = OutboundConnection.builder()
                    .target("example2.com", 80)
                    .protocol("TCP")
                    .createTime(System.currentTimeMillis() - 400000) // 过旧连接
                    .build();

            connectionCache.put("key1", connection1);
            connectionCache.put("key2", connection2);

            assertEquals(2, connectionCache.size(), "缓存应该包含2个连接");

            // 获取清理方法
            java.lang.reflect.Method cleanupMethod = 
                TcpDirectOutboundHandler.class.getDeclaredMethod("cleanupInvalidConnections");
            cleanupMethod.setAccessible(true);

            // 模拟连接计数器达到清理条件
            java.lang.reflect.Field connectionCounterField = 
                TcpDirectOutboundHandler.class.getDeclaredField("connectionCounter");
            connectionCounterField.setAccessible(true);
            java.util.concurrent.atomic.AtomicLong connectionCounter = 
                (java.util.concurrent.atomic.AtomicLong) connectionCounterField.get(outboundHandler);
            connectionCounter.set(100); // 设置为100，触发清理

            // 执行清理
            cleanupMethod.invoke(outboundHandler);

            // 验证过旧连接被清理
            assertTrue(connectionCache.size() <= 2, "清理后缓存大小应该减少或保持不变");

        } catch (Exception e) {
            fail("连接缓存清理测试失败: " + e.getMessage());
        }
    }

    @Test
    void testConnectionPoolKeyFormat() {
        // 测试连接池键值格式
        ProxyRequest request = ProxyRequest.builder()
                .target("test.example.com", 8080)
                .protocol("UDP")
                .clientChannel(testChannel)
                .clientId("test-client-123")
                .build();

        try {
            java.lang.reflect.Method getPoolKeyMethod = 
                TcpDirectOutboundHandler.class.getDeclaredMethod("getPoolKey", ProxyRequest.class);
            getPoolKeyMethod.setAccessible(true);

            String poolKey = (String) getPoolKeyMethod.invoke(outboundHandler, request);

            // 验证键值格式: host:port:protocol:clientId
            String expectedKey = "test.example.com:8080:UDP:test-client-123";
            assertEquals(expectedKey, poolKey, "连接池键值格式应该正确");

        } catch (Exception e) {
            fail("连接池键值格式测试失败: " + e.getMessage());
        }
    }

    @Test
    void testConnectionAgeCalculation() {
        // 测试连接年龄计算
        long createTime = System.currentTimeMillis() - 60000; // 1分钟前创建
        
        OutboundConnection connection = OutboundConnection.builder()
                .target("example.com", 80)
                .protocol("TCP")
                .createTime(createTime)
                .build();

        long age = connection.getConnectionAge();
        assertTrue(age >= 60000, "连接年龄应该至少为60秒");
        assertTrue(age < 70000, "连接年龄应该小于70秒"); // 允许一些时间误差
    }

    @Test
    void testConnectionIdleTime() {
        // 测试连接空闲时间计算
        OutboundConnection connection = OutboundConnection.builder()
                .target("example.com", 80)
                .protocol("TCP")
                .createTime(System.currentTimeMillis())
                .build();

        // 模拟活动
        connection.markActive();
        
        try {
            Thread.sleep(100); // 等待100毫秒
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        long idleTime = connection.getIdleTime();
        assertTrue(idleTime >= 100, "空闲时间应该至少为100毫秒");
        assertTrue(idleTime < 200, "空闲时间应该小于200毫秒"); // 允许一些时间误差
    }
}
